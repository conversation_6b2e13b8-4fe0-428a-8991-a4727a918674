using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using System.Windows.Forms;
using System.Linq;
using System;
using System.Collections.Generic;
using Autodesk.Revit.Exceptions;
using System.IO;

namespace BimexDeveloperPlugin
{
    // Classe para logging detalhado
    public static class BimexLogger
    {
        private static string logFilePath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "BIMEX_DEBUG_LOG.txt");

        public static void Log(string message)
        {
            try
            {
                string timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                string logEntry = $"[{timestamp}] {message}";

                File.AppendAllText(logFilePath, logEntry + Environment.NewLine);
                System.Diagnostics.Debug.WriteLine(logEntry);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ERRO NO LOG: {ex.Message}");
            }
        }

        public static void LogError(string operation, Exception ex)
        {
            Log($"❌ ERRO em {operation}: {ex.Message}");
            Log($"📍 Stack Trace: {ex.StackTrace}");
            if (ex.InnerException != null)
            {
                Log($"🔗 Inner Exception: {ex.InnerException.Message}");
            }
        }

        public static void LogSuccess(string operation)
        {
            Log($"✅ SUCESSO: {operation}");
        }

        public static void LogWarning(string message)
        {
            Log($"⚠️ AVISO: {message}");
        }

        public static void LogInfo(string message)
        {
            Log($"ℹ️ INFO: {message}");
        }

        public static void LogSeparator(string title)
        {
            Log($"");
            Log($"==================== {title} ====================");
        }

        public static void ClearLog()
        {
            try
            {
                if (File.Exists(logFilePath))
                {
                    File.Delete(logFilePath);
                }
                Log("🆕 NOVO LOG INICIADO - BIMEX DEVELOPER PLUGIN");
                Log($"📅 Data/Hora: {DateTime.Now}");
                Log($"💻 Usuário: {Environment.UserName}");
                Log($"📁 Diretório de trabalho: {Environment.CurrentDirectory}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao limpar log: {ex.Message}");
            }
        }

        public static string GetLogPath()
        {
            return logFilePath;
        }
    }

    [Transaction(TransactionMode.Manual)]
    public class FamilyExportCommand : IExternalCommand
    {
        public Result Execute(ExternalCommandData commandData, ref string message, ElementSet elements)
        {
            // Inicializar logging
            BimexLogger.ClearLog();
            BimexLogger.LogSeparator("INÍCIO DA EXECUÇÃO DO PLUGIN");

            UIApplication uiApp = commandData.Application;

            try
            {
                BimexLogger.LogInfo($"Revit Version: {uiApp.Application.VersionName}");
                BimexLogger.LogInfo($"Revit Build: {uiApp.Application.VersionBuild}");

                // Passo 1: Criar um novo modelo
                BimexLogger.LogSeparator("PASSO 1 - CRIANDO NOVO MODELO");
                MessageBox.Show("Passo 1: Criando novo modelo...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Document newDoc = CreateNewModel(uiApp);
                if (newDoc == null)
                {
                    MessageBox.Show("Não foi possível criar um novo modelo.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return Result.Failed;
                }

                // Passo 2: Criar piso 5x5 centralizado na origem
                MessageBox.Show("Passo 2: Criando piso 5x5 metros...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                Floor floor = CreateFloor(newDoc);
                if (floor == null)
                {
                    MessageBox.Show("Não foi possível criar o piso.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return Result.Failed;
                }

                // Passo 3: Abrir seletor de arquivo para inserir família
                MessageBox.Show("Passo 3: Selecione o arquivo da família (.rfa)", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                string familyPath = SelectFamilyFile();
                if (string.IsNullOrEmpty(familyPath))
                {
                    MessageBox.Show("Nenhuma família foi selecionada.", "Cancelado", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return Result.Cancelled;
                }

                // Passo 4: Inserir família na origem
                MessageBox.Show("Passo 4: Carregando e posicionando família...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                FamilyInstance familyInstance = LoadAndPlaceFamily(newDoc, familyPath);
                if (familyInstance == null)
                {
                    MessageBox.Show("Não foi possível carregar e posicionar a família.", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return Result.Failed;
                }

                // Passo 5: Apagar o piso
                MessageBox.Show("Passo 5: Removendo piso temporário...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                bool floorDeleted = DeleteFloor(newDoc, floor);
                if (!floorDeleted)
                {
                    MessageBox.Show("Não foi possível apagar o piso.", "Aviso", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // Passo 6: Criar e configurar vista 3D
                BimexLogger.LogSeparator("PASSO 6 - CRIANDO VISTA 3D");
                MessageBox.Show("Passo 6: Criando e configurando vista 3D...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Verificar estado do documento antes de criar vista 3D
                BimexLogger.LogInfo($"Documento para vista 3D: {newDoc.Title}");
                BimexLogger.LogInfo($"Documento é válido: {!newDoc.IsDetached}");
                BimexLogger.LogInfo($"Documento tem transações ativas: {newDoc.IsModifiable}");

                // Verificar ViewFamilyTypes disponíveis no novo documento
                var allViewTypesInNewDoc = new FilteredElementCollector(newDoc)
                    .OfClass(typeof(ViewFamilyType))
                    .Cast<ViewFamilyType>()
                    .ToList();

                BimexLogger.LogInfo($"ViewFamilyTypes no novo documento: {allViewTypesInNewDoc.Count}");
                foreach (var vft in allViewTypesInNewDoc)
                {
                    BimexLogger.LogInfo($"  - {vft.ViewFamily}: {vft.Name} (ID: {vft.Id})");
                }

                var view3DTypesInNewDoc = allViewTypesInNewDoc.Where(vft => vft.ViewFamily == ViewFamily.ThreeDimensional).ToList();
                BimexLogger.LogInfo($"ViewFamilyTypes 3D no novo documento: {view3DTypesInNewDoc.Count}");

                string view3DErrorDetails = "";
                View3D view3D = CreateAndActivate3DViewWithDetails(uiApp, newDoc, out view3DErrorDetails);

                if (view3D == null)
                {
                    BimexLogger.LogError("Criação da Vista 3D", new Exception("Vista 3D retornou null"));
                    BimexLogger.LogInfo("Detalhes do erro da vista 3D:");
                    BimexLogger.LogInfo(view3DErrorDetails);

                    string errorMessage = "❌ ERRO: Não foi possível criar a vista 3D.\n\n";
                    errorMessage += "📋 DETALHES DO ERRO:\n";
                    errorMessage += view3DErrorDetails;
                    errorMessage += $"\n\n📁 Log detalhado salvo em: {BimexLogger.GetLogPath()}";
                    errorMessage += "\n\n⚠️ O plugin continuará, mas a exportação pode não funcionar corretamente.";

                    MessageBox.Show(errorMessage, "Erro na Criação da Vista 3D", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                else
                {
                    BimexLogger.LogSuccess($"Vista 3D criada: {view3D.Name} (ID: {view3D.Id})");
                    MessageBox.Show($"✅ Vista 3D criada com sucesso!\n\nNome: {view3D.Name}\nID: {view3D.Id}\n\nA vista está pronta para visualização.\n\n📁 Log detalhado: {BimexLogger.GetLogPath()}", "Vista 3D Criada", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Passo 6.5: Abrir e mostrar a vista 3D para o usuário
                if (view3D != null)
                {
                    MessageBox.Show("Passo 6.5: Abrindo vista 3D para visualização...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    string openViewErrorDetails = "";
                    bool viewOpened = OpenAndShowView3DWithDetails(uiApp, newDoc, view3D, out openViewErrorDetails);

                    if (viewOpened)
                    {
                        MessageBox.Show("✅ Vista 3D aberta com sucesso!\n\n🎯 Você pode agora visualizar a família na vista 3D.\n\n📐 A vista foi configurada com zoom automático.\n\n➡️ Clique OK para continuar com a exportação.", "Vista 3D Ativa", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        string warningMessage = "⚠️ AVISO: Não foi possível abrir a vista 3D na interface.\n\n";
                        warningMessage += "📋 DETALHES DO PROBLEMA:\n";
                        warningMessage += openViewErrorDetails;
                        warningMessage += "\n\n🔄 A exportação continuará, mas você pode não ver a vista 3D ativa.";

                        MessageBox.Show(warningMessage, "Problema na Abertura da Vista 3D", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                else
                {
                    MessageBox.Show("⚠️ AVISO: Vista 3D não foi criada.\n\nO Passo 6.5 (visualização) será pulado.\n\nA exportação tentará continuar sem vista 3D ativa.", "Vista 3D Não Disponível", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // Passo 7: Exportar para OBJ (com vista 3D ativa)
                MessageBox.Show("Passo 7: Exportando para formato OBJ com vista 3D ativa...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                string objPath = ExportToOBJ(uiApp, newDoc, view3D);

                // Passo 8: Salvar modelo Revit (com vista 3D ativa)
                MessageBox.Show("Passo 8: Salvando modelo Revit com vista 3D...", "BIMEX Developer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                string revitPath = SaveRevitModel(newDoc);

                // Mostrar resultado final
                if (!string.IsNullOrEmpty(objPath) && !string.IsNullOrEmpty(revitPath))
                {
                    MessageBox.Show($"Processo concluído com sucesso!\n\nA família foi carregada e posicionada na origem.\n\nArquivos criados:\n• OBJ: {objPath}\n• Revit: {revitPath}", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else if (!string.IsNullOrEmpty(objPath))
                {
                    MessageBox.Show($"Processo concluído com sucesso!\n\nA família foi carregada e posicionada na origem.\n\nArquivo OBJ exportado em:\n{objPath}\n\nATENÇÃO: Não foi possível salvar o modelo Revit automaticamente.", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else if (!string.IsNullOrEmpty(revitPath))
                {
                    MessageBox.Show($"Processo concluído com sucesso!\n\nA família foi carregada e posicionada na origem.\n\nModelo Revit salvo em:\n{revitPath}\n\nATENÇÃO: Não foi possível exportar para OBJ automaticamente.", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Processo concluído com sucesso!\nA família foi carregada e posicionada na origem.\n\nATENÇÃO: Não foi possível exportar para OBJ nem salvar o modelo Revit automaticamente.", "Sucesso", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                BimexLogger.LogError("Execução do plugin", ex);
                message = $"Erro durante a execução: {ex.Message}";
                MessageBox.Show($"{message}\n\n📁 Log detalhado salvo em:\n{BimexLogger.GetLogPath()}", "Erro", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return Result.Failed;
            }
        }

        private Document CreateNewModel(UIApplication uiApp)
        {
            try
            {
                // Criar um novo documento baseado no template padrão
                Document newDoc = uiApp.Application.NewProjectDocument(UnitSystem.Metric);

                // O documento já é criado e ativado automaticamente pelo Revit
                // Verificar se temos acesso ao UIDocument
                UIDocument uiDoc = uiApp.ActiveUIDocument;
                if (uiDoc != null && uiDoc.Document.Equals(newDoc))
                {
                    // Fazer zoom para ajustar tudo na tela
                    var openViews = uiDoc.GetOpenUIViews();
                    if (openViews.Count > 0)
                    {
                        openViews.First().ZoomToFit();
                    }
                }

                return newDoc;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao criar novo modelo: {ex.Message}");
                return null;
            }
        }

        private Floor CreateFloor(Document doc)
        {
            try
            {
                using (Transaction trans = new Transaction(doc, "Criar Piso 5x5"))
                {
                    trans.Start();

                    // Criar pontos para um retângulo 5x5 metros centralizado na origem
                    double halfSize = 2.5; // 2.5 metros para cada lado (total 5m)

                    List<XYZ> points = new List<XYZ>
                    {
                        new XYZ(-halfSize, -halfSize, 0),
                        new XYZ(halfSize, -halfSize, 0),
                        new XYZ(halfSize, halfSize, 0),
                        new XYZ(-halfSize, halfSize, 0)
                    };

                    // Criar curvas para o contorno do piso
                    List<Curve> curves = new List<Curve>();
                    for (int i = 0; i < points.Count; i++)
                    {
                        int nextIndex = (i + 1) % points.Count;
                        curves.Add(Line.CreateBound(points[i], points[nextIndex]));
                    }

                    // Obter o tipo de piso padrão
                    FloorType floorType = new FilteredElementCollector(doc)
                        .OfClass(typeof(FloorType))
                        .Cast<FloorType>()
                        .FirstOrDefault();

                    if (floorType == null)
                    {
                        trans.RollBack();
                        return null;
                    }

                    // Obter o nível padrão (Level 1 ou Ground Floor)
                    Level level = new FilteredElementCollector(doc)
                        .OfClass(typeof(Level))
                        .Cast<Level>()
                        .Where(l => l.Name.Contains("1") || l.Name.ToLower().Contains("ground") || l.Name.ToLower().Contains("térreo"))
                        .FirstOrDefault();

                    if (level == null)
                    {
                        // Se não encontrar, pega o primeiro nível disponível
                        level = new FilteredElementCollector(doc)
                            .OfClass(typeof(Level))
                            .Cast<Level>()
                            .FirstOrDefault();
                    }

                    if (level == null)
                    {
                        trans.RollBack();
                        return null;
                    }

                    // Criar CurveLoop
                    CurveLoop curveLoop = CurveLoop.Create(curves);
                    List<CurveLoop> curveLoops = new List<CurveLoop> { curveLoop };

                    // Criar o piso usando o método correto
                    Floor floor = Floor.Create(doc, curveLoops, floorType.Id, level.Id);

                    trans.Commit();
                    return floor;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao criar piso: {ex.Message}");
                return null;
            }
        }

        private string SelectFamilyFile()
        {
            try
            {
                OpenFileDialog openFileDialog = new OpenFileDialog();
                openFileDialog.Title = "Selecionar Família Revit";
                openFileDialog.Filter = "Arquivos de Família Revit (*.rfa)|*.rfa";
                openFileDialog.FilterIndex = 1;
                openFileDialog.RestoreDirectory = true;

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    return openFileDialog.FileName;
                }
                return null;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao selecionar arquivo: {ex.Message}");
                return null;
            }
        }

        private FamilyInstance LoadAndPlaceFamily(Document doc, string familyPath)
        {
            try
            {
                using (Transaction trans = new Transaction(doc, "Carregar e Posicionar Família"))
                {
                    trans.Start();

                    // Carregar a família
                    Family loadedFamily = null;
                    bool loadResult = doc.LoadFamily(familyPath, out loadedFamily);

                    if (!loadResult || loadedFamily == null)
                    {
                        trans.RollBack();
                        return null;
                    }

                    // Obter o símbolo da família
                    FamilySymbol familySymbol = null;
                    ISet<ElementId> familySymbolIds = loadedFamily.GetFamilySymbolIds();
                    if (familySymbolIds.Count > 0)
                    {
                        familySymbol = doc.GetElement(familySymbolIds.First()) as FamilySymbol;
                    }

                    if (familySymbol == null)
                    {
                        trans.RollBack();
                        return null;
                    }

                    // Ativar o símbolo se necessário
                    if (!familySymbol.IsActive)
                    {
                        familySymbol.Activate();
                        doc.Regenerate();
                    }

                    // Obter o nível padrão (mesmo usado para o piso)
                    Level level = new FilteredElementCollector(doc)
                        .OfClass(typeof(Level))
                        .Cast<Level>()
                        .Where(l => l.Name.Contains("1") || l.Name.ToLower().Contains("ground") || l.Name.ToLower().Contains("térreo"))
                        .FirstOrDefault();

                    if (level == null)
                    {
                        level = new FilteredElementCollector(doc)
                            .OfClass(typeof(Level))
                            .Cast<Level>()
                            .FirstOrDefault();
                    }

                    if (level == null)
                    {
                        trans.RollBack();
                        return null;
                    }

                    // Posicionar a família na origem (0,0,0)
                    XYZ origin = new XYZ(0, 0, 0);
                    FamilyInstance familyInstance = doc.Create.NewFamilyInstance(origin, familySymbol, level, Autodesk.Revit.DB.Structure.StructuralType.NonStructural);

                    trans.Commit();
                    return familyInstance;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao carregar e posicionar família: {ex.Message}");
                return null;
            }
        }

        private bool DeleteFloor(Document doc, Floor floor)
        {
            try
            {
                using (Transaction trans = new Transaction(doc, "Apagar Piso"))
                {
                    trans.Start();

                    doc.Delete(floor.Id);

                    trans.Commit();
                    return true;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao apagar piso: {ex.Message}");
                return false;
            }
        }

        private View3D FindExisting3DView(Document doc)
        {
            try
            {
                // Procurar vista 3D existente (não template)
                var view3DCollector = new FilteredElementCollector(doc)
                    .OfClass(typeof(View3D))
                    .Cast<View3D>()
                    .Where(v => !v.IsTemplate);

                return view3DCollector.FirstOrDefault();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao procurar vista 3D existente: {ex.Message}");
                return null;
            }
        }

        private View3D CreateNew3DViewWithDetails(Document doc, System.Text.StringBuilder errorLog)
        {
            BimexLogger.LogSeparator("CRIAÇÃO DETALHADA DA VISTA 3D");

            try
            {
                BimexLogger.LogInfo($"Iniciando criação de vista 3D no documento: {doc.Title}");
                BimexLogger.LogInfo($"Documento é família: {doc.IsFamilyDocument}");
                BimexLogger.LogInfo($"Documento está modificável: {doc.IsModifiable}");

                using (Transaction trans = new Transaction(doc, "Criar Vista 3D BIMEX"))
                {
                    trans.Start();
                    BimexLogger.LogInfo("Transação iniciada para criação de vista 3D");

                    View3D view3D = null;
                    string timestamp = DateTime.Now.ToString("HHmmss");

                    // TENTATIVA 1: Método padrão com ViewFamilyType
                    try
                    {
                        BimexLogger.LogInfo("=== TENTATIVA 1: Método padrão ===");

                        var viewFamilyTypes = new FilteredElementCollector(doc)
                            .OfClass(typeof(ViewFamilyType))
                            .Cast<ViewFamilyType>()
                            .Where(vft => vft.ViewFamily == ViewFamily.ThreeDimensional)
                            .ToList();

                        BimexLogger.LogInfo($"FilteredElementCollector encontrou {viewFamilyTypes.Count} ViewFamilyTypes 3D");
                        errorLog.AppendLine($"🔍 Tentativa 1: Encontrados {viewFamilyTypes.Count} tipos de vista 3D");

                        if (viewFamilyTypes.Count > 0)
                        {
                            var viewFamilyType = viewFamilyTypes.First();
                            BimexLogger.LogInfo($"Usando ViewFamilyType: {viewFamilyType.Name} (ID: {viewFamilyType.Id})");
                            errorLog.AppendLine($"📝 Usando ViewFamilyType: {viewFamilyType.Name}");

                            BimexLogger.LogInfo("Chamando View3D.CreateIsometric...");
                            view3D = View3D.CreateIsometric(doc, viewFamilyType.Id);
                            BimexLogger.LogSuccess("View3D.CreateIsometric executado com sucesso");

                            view3D.Name = $"BIMEX_3D_Export_{timestamp}";
                            BimexLogger.LogInfo($"Nome da vista definido: {view3D.Name}");

                            errorLog.AppendLine($"✅ Vista 3D criada com método padrão: {view3D.Name}");
                        }
                        else
                        {
                            BimexLogger.LogWarning("Nenhum ViewFamilyType 3D encontrado no documento");
                            errorLog.AppendLine("❌ Nenhum ViewFamilyType 3D encontrado");

                            // Investigar por que não há ViewFamilyTypes 3D
                            BimexLogger.LogInfo("Investigando todos os ViewFamilyTypes disponíveis:");
                            var allViewTypes = new FilteredElementCollector(doc)
                                .OfClass(typeof(ViewFamilyType))
                                .Cast<ViewFamilyType>()
                                .ToList();

                            BimexLogger.LogInfo($"Total de ViewFamilyTypes: {allViewTypes.Count}");
                            foreach (var vft in allViewTypes)
                            {
                                BimexLogger.LogInfo($"  - {vft.ViewFamily}: {vft.Name} (ID: {vft.Id})");
                            }
                        }
                    }
                    catch (Exception ex1)
                    {
                        BimexLogger.LogError("Tentativa 1 de criação de vista 3D", ex1);
                        errorLog.AppendLine($"❌ Tentativa 1 falhou: {ex1.Message}");
                    }

                    // TENTATIVA 2: Método com busca mais específica
                    if (view3D == null)
                    {
                        try
                        {
                            errorLog.AppendLine("🔍 Tentativa 2: Busca específica por tipos de vista 3D");

                            var collector = new FilteredElementCollector(doc);
                            var viewFamilyTypes = collector
                                .OfClass(typeof(ViewFamilyType))
                                .ToElements()
                                .Cast<ViewFamilyType>()
                                .Where(vft => vft.ViewFamily == ViewFamily.ThreeDimensional)
                                .ToList();

                            errorLog.AppendLine($"📊 Encontrados {viewFamilyTypes.Count} ViewFamilyTypes para testar");

                            foreach (var vft in viewFamilyTypes)
                            {
                                try
                                {
                                    errorLog.AppendLine($"🧪 Testando ViewFamilyType: {vft.Name}");
                                    view3D = View3D.CreateIsometric(doc, vft.Id);
                                    view3D.Name = $"BIMEX_3D_Alt_{timestamp}";
                                    errorLog.AppendLine($"✅ Sucesso com {vft.Name}: {view3D.Name}");
                                    break;
                                }
                                catch (Exception ex)
                                {
                                    errorLog.AppendLine($"❌ Falha com {vft.Name}: {ex.Message}");
                                    view3D = null;
                                }
                            }
                        }
                        catch (Exception ex2)
                        {
                            errorLog.AppendLine($"❌ Tentativa 2 falhou: {ex2.Message}");
                        }
                    }

                    // TENTATIVA 3: Criar vista 3D perspectiva se isométrica falhou
                    if (view3D == null)
                    {
                        try
                        {
                            errorLog.AppendLine("🔍 Tentativa 3: Criando vista perspectiva");

                            var viewFamilyTypes = new FilteredElementCollector(doc)
                                .OfClass(typeof(ViewFamilyType))
                                .Cast<ViewFamilyType>()
                                .Where(vft => vft.ViewFamily == ViewFamily.ThreeDimensional);

                            if (viewFamilyTypes.Any())
                            {
                                var viewType = viewFamilyTypes.First();
                                errorLog.AppendLine($"📝 Criando vista perspectiva com: {viewType.Name}");

                                view3D = View3D.CreatePerspective(doc, viewType.Id);
                                view3D.Name = $"BIMEX_3D_Perspective_{timestamp}";

                                errorLog.AppendLine($"✅ Vista perspectiva criada: {view3D.Name}");
                            }
                            else
                            {
                                errorLog.AppendLine("❌ Nenhum ViewFamilyType disponível para perspectiva");
                            }
                        }
                        catch (Exception ex4)
                        {
                            errorLog.AppendLine($"❌ Tentativa 3 (perspectiva) falhou: {ex4.Message}");
                        }
                    }

                    // TENTATIVA 4: SOLUÇÃO FORA DA CAIXA - Criar ViewFamilyType 3D se não existir
                    if (view3D == null)
                    {
                        try
                        {
                            BimexLogger.LogInfo("=== TENTATIVA 4: SOLUÇÃO FORA DA CAIXA ===");
                            errorLog.AppendLine("🔍 Tentativa 4: Criando ViewFamilyType 3D do zero");

                            BimexLogger.LogInfo("Tentando criar ViewFamilyType 3D programaticamente...");

                            // Tentar duplicar um ViewFamilyType existente e modificá-lo
                            var anyViewType = new FilteredElementCollector(doc)
                                .OfClass(typeof(ViewFamilyType))
                                .Cast<ViewFamilyType>()
                                .FirstOrDefault();

                            if (anyViewType != null)
                            {
                                BimexLogger.LogInfo($"Tentando duplicar ViewFamilyType: {anyViewType.Name}");

                                // Método 1: Tentar duplicar elemento
                                try
                                {
                                    var duplicatedIds = ElementTransformUtils.CopyElement(doc, anyViewType.Id, XYZ.Zero);
                                    if (duplicatedIds.Count > 0)
                                    {
                                        var duplicatedViewType = doc.GetElement(duplicatedIds.First()) as ViewFamilyType;
                                        if (duplicatedViewType != null)
                                        {
                                            BimexLogger.LogInfo("ViewFamilyType duplicado com sucesso");
                                            // Tentar usar o duplicado para criar vista 3D
                                            view3D = View3D.CreateIsometric(doc, duplicatedViewType.Id);
                                            view3D.Name = $"BIMEX_3D_Duplicated_{timestamp}";
                                            errorLog.AppendLine($"✅ Vista criada com ViewFamilyType duplicado: {view3D.Name}");
                                        }
                                    }
                                }
                                catch (Exception exDup)
                                {
                                    BimexLogger.LogError("Duplicação de ViewFamilyType", exDup);
                                }
                            }

                            // Método 2: Tentar criar vista usando qualquer ViewFamilyType disponível
                            if (view3D == null)
                            {
                                BimexLogger.LogInfo("Tentando forçar criação com qualquer ViewFamilyType...");
                                var allTypes = new FilteredElementCollector(doc)
                                    .OfClass(typeof(ViewFamilyType))
                                    .Cast<ViewFamilyType>()
                                    .ToList();

                                foreach (var vft in allTypes)
                                {
                                    try
                                    {
                                        BimexLogger.LogInfo($"Testando forçar criação com: {vft.ViewFamily} - {vft.Name}");

                                        // Tentar forçar criação mesmo que não seja 3D
                                        view3D = View3D.CreateIsometric(doc, vft.Id);
                                        view3D.Name = $"BIMEX_3D_Forced_{timestamp}";
                                        BimexLogger.LogSuccess($"SUCESSO! Vista 3D criada forçadamente com {vft.ViewFamily}");
                                        errorLog.AppendLine($"✅ Vista criada forçadamente: {view3D.Name}");
                                        break;
                                    }
                                    catch (Exception exForce)
                                    {
                                        BimexLogger.LogInfo($"Falha ao forçar com {vft.Name}: {exForce.Message}");
                                    }
                                }
                            }

                            if (view3D == null)
                            {
                                errorLog.AppendLine("❌ Todas as tentativas fora da caixa falharam");
                                BimexLogger.LogWarning("TODAS AS TENTATIVAS FORA DA CAIXA FALHARAM");
                            }
                        }
                        catch (Exception ex5)
                        {
                            BimexLogger.LogError("Tentativa 4 (fora da caixa)", ex5);
                            errorLog.AppendLine($"❌ Tentativa 4 (fora da caixa) falhou: {ex5.Message}");
                        }
                    }

                    // TENTATIVA 5: ÚLTIMO RECURSO - Usar template do Revit
                    if (view3D == null)
                    {
                        try
                        {
                            BimexLogger.LogInfo("=== TENTATIVA 5: ÚLTIMO RECURSO ===");
                            errorLog.AppendLine("🔍 Tentativa 5: Último recurso - template do Revit");

                            // Tentar criar um novo documento temporário com template padrão
                            BimexLogger.LogInfo("Tentando acessar templates do sistema...");

                            // Verificar se podemos acessar templates do sistema
                            var app = doc.Application;
                            BimexLogger.LogInfo($"Aplicação Revit: {app.VersionName}");

                            // Tentar criar vista 3D usando método alternativo
                            var collector = new FilteredElementCollector(doc);
                            var elements = collector.ToElements();
                            BimexLogger.LogInfo($"Total de elementos no documento: {elements.Count}");

                            // Se ainda não conseguiu, tentar método mais básico
                            errorLog.AppendLine("❌ Último recurso também falhou");
                            BimexLogger.LogError("Último recurso", new Exception("Não foi possível criar vista 3D por nenhum método"));
                        }
                        catch (Exception ex6)
                        {
                            BimexLogger.LogError("Tentativa 5 (último recurso)", ex6);
                            errorLog.AppendLine($"❌ Tentativa 5 (último recurso) falhou: {ex6.Message}");
                        }
                    }

                    if (view3D != null)
                    {
                        trans.Commit();
                        errorLog.AppendLine($"✅ SUCESSO: Vista 3D criada - {view3D.Name} (ID: {view3D.Id})");
                    }
                    else
                    {
                        trans.RollBack();
                        errorLog.AppendLine("❌ FALHA TOTAL: Todas as tentativas de criação falharam");
                        errorLog.AppendLine("💡 DIAGNÓSTICO:");
                        errorLog.AppendLine("   • Verifique se o template do projeto tem tipos de vista 3D");
                        errorLog.AppendLine("   • Tente criar uma vista 3D manualmente no Revit");
                        errorLog.AppendLine("   • Verifique permissões do documento");
                    }

                    return view3D;
                }
            }
            catch (Exception ex)
            {
                errorLog.AppendLine($"❌ ERRO GERAL na criação: {ex.Message}");
                errorLog.AppendLine($"📍 Stack Trace: {ex.StackTrace}");
                return null;
            }
        }

        private View3D CreateNew3DView(Document doc)
        {
            var errorLog = new System.Text.StringBuilder();
            return CreateNew3DViewWithDetails(doc, errorLog);
        }

        private void Configure3DViewWithDetails(Document doc, View3D view3D, System.Text.StringBuilder errorLog)
        {
            try
            {
                using (Transaction trans = new Transaction(doc, "Configurar Vista 3D"))
                {
                    trans.Start();

                    errorLog.AppendLine($"🔧 Configurando vista 3D: {view3D.Name}");

                    // Configurações básicas para máxima qualidade
                    try
                    {
                        view3D.DetailLevel = ViewDetailLevel.Fine;
                        view3D.DisplayStyle = DisplayStyle.ShadingWithEdges;
                        errorLog.AppendLine("✅ Configurações básicas aplicadas (Fine + ShadingWithEdges)");
                    }
                    catch (Exception ex)
                    {
                        errorLog.AppendLine($"❌ Configurações básicas falharam: {ex.Message}");
                    }

                    // Configurações de crop box (desativar para ver toda a geometria)
                    try
                    {
                        view3D.CropBoxActive = false;
                        view3D.CropBoxVisible = false;
                        errorLog.AppendLine("✅ Crop box desativado (mostra toda geometria)");
                    }
                    catch (Exception ex)
                    {
                        errorLog.AppendLine($"❌ Configuração de crop box falhou: {ex.Message}");
                    }

                    // Configurar orientação isométrica padrão
                    try
                    {
                        ViewOrientation3D orientation = new ViewOrientation3D(
                            new XYZ(1, 1, 1),    // Eye position (isométrica)
                            new XYZ(0, 1, 0),    // Up direction
                            new XYZ(-1, -1, -1)  // Forward direction
                        );
                        view3D.SetOrientation(orientation);
                        errorLog.AppendLine("✅ Orientação isométrica configurada");
                    }
                    catch (Exception ex)
                    {
                        errorLog.AppendLine($"❌ Orientação isométrica falhou: {ex.Message}");

                        // Tentar orientação alternativa
                        try
                        {
                            ViewOrientation3D altOrientation = new ViewOrientation3D(
                                new XYZ(10, 10, 10),  // Eye position mais distante
                                new XYZ(0, 0, 1),     // Up direction (Z)
                                new XYZ(-1, -1, -1)   // Forward direction
                            );
                            view3D.SetOrientation(altOrientation);
                            errorLog.AppendLine("✅ Orientação alternativa configurada");
                        }
                        catch (Exception ex2)
                        {
                            errorLog.AppendLine($"❌ Orientação alternativa também falhou: {ex2.Message}");
                        }
                    }

                    // Configurações adicionais para exportação OBJ
                    try
                    {
                        // Tentar configurar outras propriedades se disponíveis
                        if (view3D.CanModifyViewDiscipline())
                        {
                            view3D.Discipline = ViewDiscipline.Architectural;
                            errorLog.AppendLine("✅ Disciplina configurada para Architectural");
                        }
                        else
                        {
                            errorLog.AppendLine("⚠️ Não foi possível modificar disciplina da vista");
                        }
                    }
                    catch (Exception ex)
                    {
                        errorLog.AppendLine($"❌ Configuração de disciplina falhou: {ex.Message}");
                    }

                    // Verificar se há geometria visível na vista
                    try
                    {
                        // Contar elementos visíveis na vista
                        var visibleElements = new FilteredElementCollector(doc, view3D.Id)
                            .WhereElementIsNotElementType()
                            .ToElements();

                        errorLog.AppendLine($"📊 Elementos visíveis na vista: {visibleElements.Count}");

                        if (visibleElements.Count == 0)
                        {
                            errorLog.AppendLine("⚠️ AVISO: Nenhum elemento visível na vista 3D");
                            errorLog.AppendLine("💡 Possíveis causas:");
                            errorLog.AppendLine("   • Família não foi carregada corretamente");
                            errorLog.AppendLine("   • Elementos estão fora do campo de visão");
                            errorLog.AppendLine("   • Filtros de vista estão ocultando elementos");
                        }
                    }
                    catch (Exception ex)
                    {
                        errorLog.AppendLine($"❌ Verificação de elementos visíveis falhou: {ex.Message}");
                    }

                    // Forçar regeneração da vista
                    try
                    {
                        doc.Regenerate();
                        errorLog.AppendLine("✅ Regeneração da vista forçada");
                    }
                    catch (Exception ex)
                    {
                        errorLog.AppendLine($"❌ Regeneração falhou: {ex.Message}");
                    }

                    trans.Commit();
                    errorLog.AppendLine($"✅ Vista 3D configurada com sucesso: {view3D.Name}");
                }
            }
            catch (Exception ex)
            {
                errorLog.AppendLine($"❌ ERRO GERAL na configuração: {ex.Message}");
                errorLog.AppendLine($"📍 Stack Trace: {ex.StackTrace}");
            }
        }

        private void Configure3DView(Document doc, View3D view3D)
        {
            var errorLog = new System.Text.StringBuilder();
            Configure3DViewWithDetails(doc, view3D, errorLog);
        }

        private View3D CreateAndActivate3DViewWithDetails(UIApplication uiApp, Document doc, out string errorDetails)
        {
            errorDetails = "";
            var errorLog = new System.Text.StringBuilder();

            try
            {
                View3D view3D = null;
                UIDocument uiDoc = null;

                // PASSO 1: Garantir que temos acesso ao UIDocument
                if (uiApp.ActiveUIDocument != null && uiApp.ActiveUIDocument.Document.Equals(doc))
                {
                    uiDoc = uiApp.ActiveUIDocument;
                    errorLog.AppendLine("✅ UIDocument disponível");
                }
                else
                {
                    errorLog.AppendLine("❌ UIDocument não disponível para ativação de vista");
                }

                // PASSO 2: Verificar ViewFamilyTypes disponíveis
                var viewFamilyTypes = new FilteredElementCollector(doc)
                    .OfClass(typeof(ViewFamilyType))
                    .Cast<ViewFamilyType>()
                    .Where(vft => vft.ViewFamily == ViewFamily.ThreeDimensional)
                    .ToList();

                errorLog.AppendLine($"📊 Encontrados {viewFamilyTypes.Count} tipos de vista 3D no documento");

                if (viewFamilyTypes.Count == 0)
                {
                    errorLog.AppendLine("❌ PROBLEMA CRÍTICO: Nenhum ViewFamilyType 3D encontrado no documento");
                    errorLog.AppendLine("💡 SOLUÇÃO: O template do projeto pode não ter tipos de vista 3D configurados");
                }

                // PASSO 3: Tentar criar nova vista 3D com detalhes
                view3D = CreateNew3DViewWithDetails(doc, errorLog);

                if (view3D == null)
                {
                    errorLog.AppendLine("⚠️ Criação de nova vista falhou, tentando encontrar vista existente...");

                    // Se falhou, tentar encontrar vista existente como fallback
                    view3D = FindExisting3DView(doc);
                    if (view3D != null)
                    {
                        errorLog.AppendLine($"✅ Usando vista 3D existente: {view3D.Name}");
                    }
                    else
                    {
                        errorLog.AppendLine("❌ Nenhuma vista 3D existente encontrada");
                    }
                }

                // PASSO 4: Configurar vista se foi criada/encontrada
                if (view3D != null)
                {
                    Configure3DViewWithDetails(doc, view3D, errorLog);
                    errorLog.AppendLine($"✅ Vista 3D configurada: {view3D.Name}");
                }
                else
                {
                    errorLog.AppendLine("❌ ERRO CRÍTICO: Não foi possível criar ou encontrar vista 3D");
                    errorLog.AppendLine("💡 POSSÍVEIS CAUSAS:");
                    errorLog.AppendLine("   • Template do projeto sem tipos de vista 3D");
                    errorLog.AppendLine("   • Permissões insuficientes");
                    errorLog.AppendLine("   • Documento corrompido");

                    errorDetails = errorLog.ToString();
                    return null;
                }

                // PASSO 5: Ativar a vista na UI com múltiplas tentativas
                if (view3D != null && uiDoc != null)
                {
                    bool activationSuccess = false;

                    // TENTATIVA 1: Ativação direta
                    try
                    {
                        uiDoc.ActiveView = view3D;
                        uiDoc.RefreshActiveView();
                        doc.Regenerate();
                        activationSuccess = true;
                        errorLog.AppendLine("✅ Vista 3D ativada com método direto");
                    }
                    catch (Exception ex1)
                    {
                        errorLog.AppendLine($"❌ Ativação direta falhou: {ex1.Message}");
                    }

                    // TENTATIVA 2: Forçar abertura da vista se ativação direta falhou
                    if (!activationSuccess)
                    {
                        try
                        {
                            uiDoc.RequestViewChange(view3D);
                            System.Threading.Thread.Sleep(500);
                            doc.Regenerate();
                            activationSuccess = true;
                            errorLog.AppendLine("✅ Vista 3D ativada com RequestViewChange");
                        }
                        catch (Exception ex2)
                        {
                            errorLog.AppendLine($"❌ RequestViewChange falhou: {ex2.Message}");
                        }
                    }

                    if (!activationSuccess)
                    {
                        errorLog.AppendLine("⚠️ Não foi possível ativar a vista 3D na interface");
                    }
                }

                // PASSO 6: Validação final
                if (view3D != null)
                {
                    doc.Regenerate();
                    errorLog.AppendLine("✅ Vista 3D criada e configurada com sucesso");
                }

                errorDetails = errorLog.ToString();
                return view3D;
            }
            catch (Exception ex)
            {
                errorLog.AppendLine($"❌ ERRO GERAL: {ex.Message}");
                errorLog.AppendLine($"📍 Stack Trace: {ex.StackTrace}");
                errorDetails = errorLog.ToString();
                return null;
            }
        }

        private View3D CreateAndActivate3DView(UIApplication uiApp, Document doc)
        {
            string errorDetails;
            return CreateAndActivate3DViewWithDetails(uiApp, doc, out errorDetails);
        }

        private void ActivateView3DInUI(UIApplication uiApp, Document doc, View3D view3D)
        {
            try
            {
                UIDocument uiDoc = null;

                // Garantir acesso ao UIDocument
                if (uiApp.ActiveUIDocument != null && uiApp.ActiveUIDocument.Document.Equals(doc))
                {
                    uiDoc = uiApp.ActiveUIDocument;
                }

                if (uiDoc != null && view3D != null)
                {
                    // CRÍTICO: Ativar a vista 3D na UI
                    uiDoc.ActiveView = view3D;
                    uiDoc.RefreshActiveView();

                    // Forçar regeneração
                    doc.Regenerate();

                    // Zoom para ajustar
                    try
                    {
                        var openViews = uiDoc.GetOpenUIViews();
                        var activeUIView = openViews.FirstOrDefault(v => v.ViewId == view3D.Id);
                        if (activeUIView != null)
                        {
                            activeUIView.ZoomToFit();
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Zoom falhou: {ex.Message}");
                    }

                    System.Diagnostics.Debug.WriteLine($"Vista 3D ativada na UI: {view3D.Name}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao ativar vista 3D na UI: {ex.Message}");
            }
        }

        private bool OpenAndShowView3DWithDetails(UIApplication uiApp, Document doc, View3D view3D, out string errorDetails)
        {
            errorDetails = "";
            var errorLog = new System.Text.StringBuilder();

            try
            {
                errorLog.AppendLine("=== ABRINDO VISTA 3D PARA VISUALIZAÇÃO DO USUÁRIO ===");

                UIDocument uiDoc = null;

                // Garantir acesso ao UIDocument
                if (uiApp.ActiveUIDocument != null && uiApp.ActiveUIDocument.Document.Equals(doc))
                {
                    uiDoc = uiApp.ActiveUIDocument;
                    errorLog.AppendLine("✅ UIDocument disponível");
                }
                else
                {
                    errorLog.AppendLine("❌ ERRO: UIDocument não disponível");
                    errorDetails = errorLog.ToString();
                    return false;
                }

                if (view3D == null)
                {
                    errorLog.AppendLine("❌ ERRO: Vista 3D não fornecida");
                    errorDetails = errorLog.ToString();
                    return false;
                }

                errorLog.AppendLine($"🎯 Abrindo vista 3D: {view3D.Name} (ID: {view3D.Id})");

                bool viewOpened = false;

                // MÉTODO 1: RequestViewChange (mais próximo do comportamento do usuário)
                try
                {
                    errorLog.AppendLine("🔍 Tentativa 1: RequestViewChange");
                    uiDoc.RequestViewChange(view3D);

                    // Aguardar um tempo maior para garantir que a vista seja aberta
                    System.Threading.Thread.Sleep(1500);

                    // Verificar se a vista foi realmente ativada
                    if (uiDoc.ActiveView != null && uiDoc.ActiveView.Id == view3D.Id)
                    {
                        viewOpened = true;
                        errorLog.AppendLine("✅ Vista 3D aberta com RequestViewChange");
                    }
                    else
                    {
                        errorLog.AppendLine($"❌ RequestViewChange não ativou a vista. Vista ativa: {uiDoc.ActiveView?.Name}");
                    }
                }
                catch (Exception ex1)
                {
                    errorLog.AppendLine($"❌ RequestViewChange falhou: {ex1.Message}");
                }

                // MÉTODO 2: Ativação direta se RequestViewChange falhou
                if (!viewOpened)
                {
                    try
                    {
                        errorLog.AppendLine("🔍 Tentativa 2: Ativação direta");
                        uiDoc.ActiveView = view3D;
                        uiDoc.RefreshActiveView();
                        doc.Regenerate();

                        // Aguardar processamento
                        System.Threading.Thread.Sleep(1000);

                        if (uiDoc.ActiveView != null && uiDoc.ActiveView.Id == view3D.Id)
                        {
                            viewOpened = true;
                            errorLog.AppendLine("✅ Vista 3D ativada com método direto");
                        }
                        else
                        {
                            errorLog.AppendLine($"❌ Ativação direta falhou. Vista ativa: {uiDoc.ActiveView?.Name}");
                        }
                    }
                    catch (Exception ex2)
                    {
                        errorLog.AppendLine($"❌ Ativação direta falhou: {ex2.Message}");
                    }
                }

                // MÉTODO 3: Forçar abertura da vista se ainda não funcionou
                if (!viewOpened)
                {
                    try
                    {
                        errorLog.AppendLine("🔍 Tentativa 3: Forçar abertura");

                        // Tentar forçar a abertura da vista
                        using (Transaction trans = new Transaction(doc, "Forçar Vista 3D"))
                        {
                            trans.Start();

                            // Regenerar documento
                            doc.Regenerate();

                            trans.Commit();
                        }

                        // Tentar ativar novamente
                        uiDoc.ActiveView = view3D;
                        uiDoc.RefreshActiveView();

                        System.Threading.Thread.Sleep(500);

                        if (uiDoc.ActiveView != null && uiDoc.ActiveView.Id == view3D.Id)
                        {
                            viewOpened = true;
                            errorLog.AppendLine("✅ Vista 3D forçada com sucesso");
                        }
                        else
                        {
                            errorLog.AppendLine($"❌ Forçar abertura falhou. Vista ativa: {uiDoc.ActiveView?.Name}");
                        }
                    }
                    catch (Exception ex3)
                    {
                        errorLog.AppendLine($"❌ Forçar abertura falhou: {ex3.Message}");
                    }
                }

                // Se conseguiu abrir a vista, aplicar zoom e configurações finais
                if (viewOpened)
                {
                    try
                    {
                        errorLog.AppendLine("🔧 Aplicando configurações finais na vista 3D...");

                        // Zoom para ajustar todo o conteúdo
                        var openViews = uiDoc.GetOpenUIViews();
                        var activeUIView = openViews.FirstOrDefault(v => v.ViewId == view3D.Id);
                        if (activeUIView != null)
                        {
                            activeUIView.ZoomToFit();
                            errorLog.AppendLine("✅ Zoom aplicado na vista 3D");
                        }
                        else
                        {
                            // Fallback: zoom na primeira vista disponível
                            if (openViews.Count > 0)
                            {
                                openViews.First().ZoomToFit();
                                errorLog.AppendLine("✅ Zoom aplicado na primeira vista disponível");
                            }
                            else
                            {
                                errorLog.AppendLine("⚠️ Nenhuma vista UI disponível para zoom");
                            }
                        }

                        // Forçar regeneração final
                        doc.Regenerate();

                        errorLog.AppendLine("=== VISTA 3D ABERTA E CONFIGURADA COM SUCESSO ===");
                        errorLog.AppendLine($"Vista ativa: {uiDoc.ActiveView.Name} (ID: {uiDoc.ActiveView.Id})");

                        errorDetails = errorLog.ToString();
                        return true;
                    }
                    catch (Exception ex)
                    {
                        errorLog.AppendLine($"❌ Erro nas configurações finais: {ex.Message}");
                        // Mesmo com erro nas configurações finais, a vista foi aberta
                        errorDetails = errorLog.ToString();
                        return true;
                    }
                }
                else
                {
                    errorLog.AppendLine("❌ FALHA: Não foi possível abrir a vista 3D");
                    if (uiDoc.ActiveView != null)
                    {
                        errorLog.AppendLine($"Vista ativa atual: {uiDoc.ActiveView.Name} (ID: {uiDoc.ActiveView.Id})");
                    }
                    errorLog.AppendLine("💡 POSSÍVEIS SOLUÇÕES:");
                    errorLog.AppendLine("   • Tente abrir a vista 3D manualmente no Revit");
                    errorLog.AppendLine("   • Verifique se a vista foi criada corretamente");
                    errorLog.AppendLine("   • Reinicie o Revit se o problema persistir");

                    errorDetails = errorLog.ToString();
                    return false;
                }
            }
            catch (Exception ex)
            {
                errorLog.AppendLine($"❌ ERRO GERAL ao abrir vista 3D: {ex.Message}");
                errorLog.AppendLine($"📍 Stack Trace: {ex.StackTrace}");
                errorDetails = errorLog.ToString();
                return false;
            }
        }

        private bool OpenAndShowView3D(UIApplication uiApp, Document doc, View3D view3D)
        {
            string errorDetails;
            return OpenAndShowView3DWithDetails(uiApp, doc, view3D, out errorDetails);
        }

        private bool TryNativeOBJExport(Document doc, View3D view3D, string fullPath)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Tentando exportação OBJ customizada de alta qualidade...");

                if (view3D == null)
                {
                    System.Diagnostics.Debug.WriteLine("Vista 3D não disponível para exportação");
                    return false;
                }

                // Usar método customizado de alta qualidade
                string objContent = CreateDetailedOBJ(doc, view3D);

                if (!string.IsNullOrEmpty(objContent))
                {
                    System.IO.File.WriteAllText(fullPath, objContent);

                    if (System.IO.File.Exists(fullPath))
                    {
                        var fileInfo = new System.IO.FileInfo(fullPath);
                        if (fileInfo.Length > 100) // Verificar se tem conteúdo
                        {
                            System.Diagnostics.Debug.WriteLine($"✓ Exportação OBJ customizada bem-sucedida: {fullPath} ({fileInfo.Length} bytes)");
                            return true;
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine("✗ Exportação OBJ customizada falhou - arquivo vazio ou não criado");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"✗ Erro geral na exportação OBJ customizada: {ex.Message}");
                return false;
            }
        }

        private bool TryAlternativeOBJExport(Document doc, View3D view3D, string fullPath)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Tentando exportação OBJ alternativa...");

                // Usar método customizado mas com vista 3D ativa
                string objContent = CreateDetailedOBJ(doc, view3D);

                if (!string.IsNullOrEmpty(objContent))
                {
                    System.IO.File.WriteAllText(fullPath, objContent);

                    if (System.IO.File.Exists(fullPath))
                    {
                        var fileInfo = new System.IO.FileInfo(fullPath);
                        if (fileInfo.Length > 100) // Verificar se tem conteúdo
                        {
                            System.Diagnostics.Debug.WriteLine($"Exportação alternativa bem-sucedida. Tamanho: {fileInfo.Length} bytes");
                            return true;
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine("Exportação alternativa falhou");
                return false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro na exportação OBJ alternativa: {ex.Message}");
                return false;
            }
        }

        private string ExportToOBJ(UIApplication uiApp, Document doc, View3D activeView3D)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("=== INICIANDO EXPORTAÇÃO OBJ ===");

                // Criar pasta BIMEX na área de trabalho
                string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                string bimexFolder = System.IO.Path.Combine(desktopPath, "BIMEX_OBJ_Exports");
                System.IO.Directory.CreateDirectory(bimexFolder);

                // Gerar nome do arquivo com timestamp
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string fileName = $"BIMEX_FamilyExport_{timestamp}";
                string fullPath = System.IO.Path.Combine(bimexFolder, fileName + ".obj");

                System.Diagnostics.Debug.WriteLine($"Arquivo de destino: {fullPath}");

                // PASSO CRÍTICO: Garantir que a vista 3D está ATIVA na UI antes da exportação
                View3D view3D = activeView3D;

                if (view3D == null)
                {
                    System.Diagnostics.Debug.WriteLine("Vista 3D não fornecida, tentando encontrar/criar uma...");
                    view3D = EnsureProper3DView(doc);
                }

                if (view3D == null)
                {
                    System.Diagnostics.Debug.WriteLine("ERRO: Não foi possível configurar vista 3D - exportação cancelada");
                    return null;
                }

                System.Diagnostics.Debug.WriteLine($"Usando vista 3D: {view3D.Name} (ID: {view3D.Id})");

                // GARANTIR que a vista 3D está REALMENTE ABERTA na UI como um usuário faria
                UIDocument uiDoc = uiApp.ActiveUIDocument;
                if (uiDoc == null)
                {
                    System.Diagnostics.Debug.WriteLine("ERRO: UIDocument não disponível");
                    return null;
                }

                // PASSO 1: Abrir a vista 3D na interface (como usuário faria)
                bool viewOpened = false;
                try
                {
                    // Método 1: Usar RequestViewChange (mais próximo do comportamento do usuário)
                    uiDoc.RequestViewChange(view3D);
                    System.Threading.Thread.Sleep(1000); // Aguardar abertura da vista

                    if (uiDoc.ActiveView != null && uiDoc.ActiveView.Id == view3D.Id)
                    {
                        viewOpened = true;
                        System.Diagnostics.Debug.WriteLine("✓ Vista 3D aberta com RequestViewChange");
                    }
                }
                catch (Exception ex1)
                {
                    System.Diagnostics.Debug.WriteLine($"RequestViewChange falhou: {ex1.Message}");
                }

                // PASSO 2: Fallback - ativação direta se RequestViewChange falhou
                if (!viewOpened)
                {
                    try
                    {
                        uiDoc.ActiveView = view3D;
                        uiDoc.RefreshActiveView();
                        doc.Regenerate();
                        System.Threading.Thread.Sleep(500);

                        if (uiDoc.ActiveView != null && uiDoc.ActiveView.Id == view3D.Id)
                        {
                            viewOpened = true;
                            System.Diagnostics.Debug.WriteLine("✓ Vista 3D ativada com método direto");
                        }
                    }
                    catch (Exception ex2)
                    {
                        System.Diagnostics.Debug.WriteLine($"Ativação direta falhou: {ex2.Message}");
                    }
                }

                // PASSO 3: Verificar se a vista está realmente ativa
                if (!viewOpened || uiDoc.ActiveView == null || uiDoc.ActiveView.Id != view3D.Id)
                {
                    System.Diagnostics.Debug.WriteLine("✗ FALHA CRÍTICA: Não foi possível abrir a vista 3D na interface");
                    if (uiDoc.ActiveView != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"Vista ativa atual: {uiDoc.ActiveView.Name} (ID: {uiDoc.ActiveView.Id})");
                    }
                    return null;
                }

                System.Diagnostics.Debug.WriteLine("✓ Vista 3D confirmada como ativa e aberta na interface");

                // PASSO 4: Zoom para ajustar conteúdo (como usuário faria)
                try
                {
                    var openViews = uiDoc.GetOpenUIViews();
                    var activeUIView = openViews.FirstOrDefault(v => v.ViewId == view3D.Id);
                    if (activeUIView != null)
                    {
                        activeUIView.ZoomToFit();
                        System.Diagnostics.Debug.WriteLine("✓ Zoom aplicado na vista 3D");
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Zoom falhou: {ex.Message}");
                }

                // TENTATIVA 1: Exportação customizada de alta qualidade
                System.Diagnostics.Debug.WriteLine("=== TENTANDO EXPORTAÇÃO CUSTOMIZADA DE ALTA QUALIDADE ===");

                // Aguardar um momento para garantir que a vista está completamente carregada
                System.Threading.Thread.Sleep(500);
                doc.Regenerate();

                bool customExportSuccess = TryNativeOBJExport(doc, view3D, fullPath);

                if (customExportSuccess && System.IO.File.Exists(fullPath))
                {
                    var fileInfo = new System.IO.FileInfo(fullPath);
                    if (fileInfo.Length > 100)
                    {
                        System.Diagnostics.Debug.WriteLine("=== EXPORTAÇÃO OBJ CUSTOMIZADA CONCLUÍDA COM SUCESSO ===");
                        return fullPath;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠ Arquivo OBJ customizado muito pequeno ({fileInfo.Length} bytes)");
                    }
                }

                // TENTATIVA 2: Exportação customizada como fallback
                System.Diagnostics.Debug.WriteLine("=== TENTANDO EXPORTAÇÃO CUSTOMIZADA COMO FALLBACK ===");

                using (Transaction trans = new Transaction(doc, "Exportar OBJ Customizado"))
                {
                    trans.Start();

                    // Forçar regeneração antes da exportação
                    doc.Regenerate();

                    bool exportResult = false;
                    string objContent = "";

                    try
                    {
                        // Criar arquivo OBJ com geometria real usando vista 3D configurada
                        objContent = CreateDetailedOBJ(doc, view3D);

                        if (!string.IsNullOrEmpty(objContent))
                        {
                            System.IO.File.WriteAllText(fullPath, objContent);
                            exportResult = true;
                            System.Diagnostics.Debug.WriteLine($"✓ Exportação OBJ customizada bem-sucedida: {fullPath}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine("✗ Conteúdo OBJ vazio");
                        }
                    }
                    catch (Exception ex1)
                    {
                        System.Diagnostics.Debug.WriteLine($"✗ Método customizado falhou: {ex1.Message}");

                        // TENTATIVA 3: Fallback com método simples
                        try
                        {
                            System.Diagnostics.Debug.WriteLine("Tentando método simples...");
                            objContent = CreateSimpleOBJ(doc);

                            if (!string.IsNullOrEmpty(objContent))
                            {
                                System.IO.File.WriteAllText(fullPath, objContent);
                                exportResult = true;
                                System.Diagnostics.Debug.WriteLine($"✓ Exportação OBJ simples bem-sucedida: {fullPath}");
                            }
                        }
                        catch (Exception ex2)
                        {
                            System.Diagnostics.Debug.WriteLine($"✗ Método simples falhou: {ex2.Message}");
                            exportResult = false;
                        }
                    }

                    trans.Commit();

                    // Verificar resultado da exportação customizada
                    if (exportResult && System.IO.File.Exists(fullPath))
                    {
                        var fileInfo = new System.IO.FileInfo(fullPath);
                        System.Diagnostics.Debug.WriteLine($"Arquivo customizado criado com {fileInfo.Length} bytes");

                        if (fileInfo.Length > 100) // Arquivo deve ter pelo menos 100 bytes
                        {
                            System.Diagnostics.Debug.WriteLine("=== EXPORTAÇÃO OBJ CUSTOMIZADA CONCLUÍDA COM SUCESSO ===");
                            return fullPath;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠ Arquivo OBJ customizado muito pequeno ({fileInfo.Length} bytes)");
                        }
                    }
                }

                // TENTATIVA 3: Arquivo de emergência se tudo falhou
                System.Diagnostics.Debug.WriteLine("Criando arquivo de emergência...");
                try
                {
                    string emergencyContent = CreateEmergencyOBJ(doc);
                    System.IO.File.WriteAllText(fullPath, emergencyContent);

                    var fileInfo = new System.IO.FileInfo(fullPath);
                    System.Diagnostics.Debug.WriteLine($"✓ Arquivo de emergência criado com {fileInfo.Length} bytes");

                    return fullPath;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"✗ Criação de arquivo de emergência falhou: {ex.Message}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"✗ Erro geral na exportação OBJ: {ex.Message}");
                return null;
            }
        }

        private string CreateDetailedOBJ(Document doc, View3D view3D = null)
        {
            try
            {
                var objContent = new System.Text.StringBuilder();
                objContent.AppendLine("# BIMEX Family Export - MAXIMUM QUALITY OBJ");
                objContent.AppendLine("# Generated by BIMEX Developer Plugin");
                objContent.AppendLine($"# Export Date: {DateTime.Now}");
                objContent.AppendLine("# Quality: MAXIMUM - No decimation, full detail preservation");
                objContent.AppendLine($"# 3D View: {(view3D != null ? view3D.Name : "None")}");
                objContent.AppendLine();

                int vertexCount = 1; // OBJ vertices start at 1
                int normalCount = 1; // Normal vectors start at 1

                // Coletar todas as instâncias de família no documento
                var familyInstances = new FilteredElementCollector(doc)
                    .OfClass(typeof(FamilyInstance))
                    .Cast<FamilyInstance>()
                    .ToList();

                if (familyInstances.Count > 0)
                {
                    objContent.AppendLine($"# Found {familyInstances.Count} family instance(s)");
                    objContent.AppendLine("# Extracting geometry with MAXIMUM quality settings");
                    objContent.AppendLine();

                    foreach (var familyInstance in familyInstances)
                    {
                        objContent.AppendLine($"# Family: {familyInstance.Symbol.FamilyName}");
                        objContent.AppendLine($"# Type: {familyInstance.Symbol.Name}");
                        objContent.AppendLine($"# Element ID: {familyInstance.Id}");
                        objContent.AppendLine($"g {familyInstance.Symbol.FamilyName}_{familyInstance.Id}");
                        objContent.AppendLine();

                        // Múltiplas tentativas com diferentes configurações de geometria para máxima qualidade
                        bool geometryProcessed = false;

                        // TENTATIVA 1: Configuração ULTRA-ALTA qualidade
                        Options ultraHighOptions = new Options();
                        ultraHighOptions.DetailLevel = ViewDetailLevel.Fine;
                        ultraHighOptions.IncludeNonVisibleObjects = true;
                        ultraHighOptions.ComputeReferences = true;

                        // Tentar obter geometria da instância primeiro
                        GeometryElement geomElement = familyInstance.get_Geometry(ultraHighOptions);
                        if (geomElement != null)
                        {
                            objContent.AppendLine("# Using ULTRA-HIGH quality instance geometry");
                            var result = ProcessGeometryMaxQuality(geomElement, objContent, vertexCount, normalCount, familyInstance.GetTransform());
                            vertexCount = result.Item1;
                            normalCount = result.Item2;
                            geometryProcessed = true;
                        }

                        // TENTATIVA 2: Geometria do símbolo da família se instância falhou
                        if (!geometryProcessed)
                        {
                            GeometryElement symbolGeom = familyInstance.Symbol.get_Geometry(ultraHighOptions);
                            if (symbolGeom != null)
                            {
                                objContent.AppendLine("# Using ULTRA-HIGH quality symbol geometry");
                                var result = ProcessGeometryMaxQuality(symbolGeom, objContent, vertexCount, normalCount, familyInstance.GetTransform());
                                vertexCount = result.Item1;
                                normalCount = result.Item2;
                                geometryProcessed = true;
                            }
                        }

                        // TENTATIVA 3: Geometria da família (documento da família)
                        if (!geometryProcessed)
                        {
                            try
                            {
                                Document familyDoc = familyInstance.Symbol.Family.Document;
                                if (familyDoc != null)
                                {
                                    objContent.AppendLine("# Using family document geometry");
                                    var familyGeometry = ExtractFamilyDocumentGeometry(familyDoc, objContent, vertexCount, normalCount);
                                    vertexCount = familyGeometry.Item1;
                                    normalCount = familyGeometry.Item2;
                                    geometryProcessed = true;
                                }
                            }
                            catch (Exception ex)
                            {
                                objContent.AppendLine($"# Family document access failed: {ex.Message}");
                            }
                        }

                        // TENTATIVA 4: Configuração alternativa com vista específica
                        if (!geometryProcessed && view3D != null)
                        {
                            Options viewOptions = new Options();
                            viewOptions.View = view3D;
                            viewOptions.DetailLevel = ViewDetailLevel.Fine;
                            viewOptions.IncludeNonVisibleObjects = true;
                            viewOptions.ComputeReferences = true;

                            GeometryElement viewGeom = familyInstance.get_Geometry(viewOptions);
                            if (viewGeom != null)
                            {
                                objContent.AppendLine("# Using 3D view-specific geometry");
                                var result = ProcessGeometryMaxQuality(viewGeom, objContent, vertexCount, normalCount, familyInstance.GetTransform());
                                vertexCount = result.Item1;
                                normalCount = result.Item2;
                                geometryProcessed = true;
                            }
                        }

                        // ÚLTIMO RECURSO: Bounding box de alta qualidade
                        if (!geometryProcessed)
                        {
                            objContent.AppendLine("# Using high quality bounding box as fallback");
                            BoundingBoxXYZ bbox = familyInstance.get_BoundingBox(null);
                            if (bbox != null)
                            {
                                vertexCount = CreateHighQualityBoundingBoxOBJ(bbox, objContent, vertexCount);
                            }
                        }

                        objContent.AppendLine();
                    }
                }
                else
                {
                    // Se não há instâncias de família, criar um cubo de alta qualidade
                    objContent.AppendLine("# No family instances found - creating high quality reference cube");
                    objContent.AppendLine("g HighQualityReferenceCube");
                    objContent.AppendLine();
                    CreateHighQualityCube(objContent);
                }

                objContent.AppendLine($"# Total vertices: {vertexCount - 1}");
                objContent.AppendLine($"# Total normals: {normalCount - 1}");
                objContent.AppendLine("# Export completed with MAXIMUM quality settings");

                return objContent.ToString();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao criar OBJ detalhado: {ex.Message}");
                return CreateSimpleOBJ(doc);
            }
        }

        private (int, int) ProcessGeometryMaxQuality(GeometryElement geomElement, System.Text.StringBuilder objContent, int vertexCount, int normalCount, Transform transform)
        {
            try
            {
                foreach (GeometryObject geomObj in geomElement)
                {
                    if (geomObj is Solid solid && solid.Volume > 0)
                    {
                        var result = ProcessSolidMaxQuality(solid, objContent, vertexCount, normalCount, transform);
                        vertexCount = result.Item1;
                        normalCount = result.Item2;
                    }
                    else if (geomObj is GeometryInstance geomInstance)
                    {
                        GeometryElement instanceGeom = geomInstance.GetInstanceGeometry();
                        if (instanceGeom != null)
                        {
                            Transform combinedTransform = transform.Multiply(geomInstance.Transform);
                            var result = ProcessGeometryMaxQuality(instanceGeom, objContent, vertexCount, normalCount, combinedTransform);
                            vertexCount = result.Item1;
                            normalCount = result.Item2;
                        }
                    }
                    else if (geomObj is Mesh mesh)
                    {
                        var result = ProcessMeshMaxQuality(mesh, objContent, vertexCount, normalCount, transform);
                        vertexCount = result.Item1;
                        normalCount = result.Item2;
                    }
                    else if (geomObj is Curve curve)
                    {
                        // Processar curvas como linhas de alta qualidade
                        vertexCount = ProcessCurveMaxQuality(curve, objContent, vertexCount, transform);
                    }
                }
                return (vertexCount, normalCount);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao processar geometria: {ex.Message}");
                return (vertexCount, normalCount);
            }
        }

        private int ProcessGeometry(GeometryElement geomElement, System.Text.StringBuilder objContent, int vertexCount)
        {
            try
            {
                foreach (GeometryObject geomObj in geomElement)
                {
                    if (geomObj is Solid solid && solid.Volume > 0)
                    {
                        vertexCount = ProcessSolid(solid, objContent, vertexCount);
                    }
                    else if (geomObj is GeometryInstance geomInstance)
                    {
                        GeometryElement instanceGeom = geomInstance.GetInstanceGeometry();
                        if (instanceGeom != null)
                        {
                            vertexCount = ProcessGeometry(instanceGeom, objContent, vertexCount);
                        }
                    }
                }
                return vertexCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao processar geometria: {ex.Message}");
                return vertexCount;
            }
        }

        private (int, int) ProcessSolidMaxQuality(Solid solid, System.Text.StringBuilder objContent, int vertexCount, int normalCount, Transform transform)
        {
            try
            {
                objContent.AppendLine($"# Processing solid with {solid.Faces.Size} faces - MAXIMUM quality");

                foreach (Face face in solid.Faces)
                {
                    // Usar triangulação de ULTRA-ALTA qualidade
                    Mesh mesh = face.Triangulate(0.001); // Tolerância ultra-baixa para máxima qualidade (10x menor)
                    if (mesh != null)
                    {
                        objContent.AppendLine($"# Face with {mesh.NumTriangles} triangles");

                        // Processar todos os vértices únicos primeiro com deduplicação inteligente
                        var uniqueVertices = new List<XYZ>();
                        var vertexMap = new Dictionary<string, int>(); // Para evitar vértices duplicados

                        for (int i = 0; i < mesh.Vertices.Count; i++)
                        {
                            XYZ vertex = mesh.Vertices[i];
                            XYZ transformedVertex = transform.OfPoint(vertex);

                            // Criar chave única para o vértice (com precisão de 8 dígitos)
                            string vertexKey = $"{transformedVertex.X:F8}_{transformedVertex.Y:F8}_{transformedVertex.Z:F8}";

                            if (!vertexMap.ContainsKey(vertexKey))
                            {
                                uniqueVertices.Add(transformedVertex);
                                vertexMap[vertexKey] = uniqueVertices.Count - 1;
                                objContent.AppendLine($"v {transformedVertex.X:F8} {transformedVertex.Y:F8} {transformedVertex.Z:F8}");
                            }
                        }

                        // Calcular e adicionar normais para cada triângulo usando mapeamento de vértices únicos
                        for (int i = 0; i < mesh.NumTriangles; i++)
                        {
                            MeshTriangle triangle = mesh.get_Triangle(i);

                            int originalIdx1 = (int)triangle.get_Index(0);
                            int originalIdx2 = (int)triangle.get_Index(1);
                            int originalIdx3 = (int)triangle.get_Index(2);

                            // Obter vértices originais e transformados
                            XYZ originalV1 = mesh.Vertices[originalIdx1];
                            XYZ originalV2 = mesh.Vertices[originalIdx2];
                            XYZ originalV3 = mesh.Vertices[originalIdx3];

                            XYZ v1 = transform.OfPoint(originalV1);
                            XYZ v2 = transform.OfPoint(originalV2);
                            XYZ v3 = transform.OfPoint(originalV3);

                            // Encontrar índices dos vértices únicos
                            string key1 = $"{v1.X:F8}_{v1.Y:F8}_{v1.Z:F8}";
                            string key2 = $"{v2.X:F8}_{v2.Y:F8}_{v2.Z:F8}";
                            string key3 = $"{v3.X:F8}_{v3.Y:F8}_{v3.Z:F8}";

                            int uniqueIdx1 = vertexMap[key1];
                            int uniqueIdx2 = vertexMap[key2];
                            int uniqueIdx3 = vertexMap[key3];

                            // Calcular normal do triângulo com maior precisão
                            XYZ edge1 = v2 - v1;
                            XYZ edge2 = v3 - v1;
                            XYZ normal = edge1.CrossProduct(edge2);

                            // Normalizar apenas se o vetor não for zero
                            if (normal.GetLength() > 1e-10)
                            {
                                normal = normal.Normalize();
                            }
                            else
                            {
                                normal = new XYZ(0, 0, 1); // Normal padrão para triângulos degenerados
                            }

                            objContent.AppendLine($"vn {normal.X:F8} {normal.Y:F8} {normal.Z:F8}");

                            // Criar face com normais usando índices únicos
                            int faceV1 = vertexCount + uniqueIdx1;
                            int faceV2 = vertexCount + uniqueIdx2;
                            int faceV3 = vertexCount + uniqueIdx3;

                            objContent.AppendLine($"f {faceV1}//{normalCount} {faceV2}//{normalCount} {faceV3}//{normalCount}");
                            normalCount++;
                        }

                        vertexCount += uniqueVertices.Count;
                    }
                }
                return (vertexCount, normalCount);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao processar sólido: {ex.Message}");
                return (vertexCount, normalCount);
            }
        }

        private (int, int) ProcessMeshMaxQuality(Mesh mesh, System.Text.StringBuilder objContent, int vertexCount, int normalCount, Transform transform)
        {
            try
            {
                objContent.AppendLine($"# Processing mesh with {mesh.NumTriangles} triangles - MAXIMUM quality");

                // Processar todos os vértices únicos com deduplicação inteligente
                var uniqueVertices = new List<XYZ>();
                var vertexMap = new Dictionary<string, int>(); // Para evitar vértices duplicados

                for (int i = 0; i < mesh.Vertices.Count; i++)
                {
                    XYZ vertex = mesh.Vertices[i];
                    XYZ transformedVertex = transform.OfPoint(vertex);

                    // Criar chave única para o vértice (com precisão de 8 dígitos)
                    string vertexKey = $"{transformedVertex.X:F8}_{transformedVertex.Y:F8}_{transformedVertex.Z:F8}";

                    if (!vertexMap.ContainsKey(vertexKey))
                    {
                        uniqueVertices.Add(transformedVertex);
                        vertexMap[vertexKey] = uniqueVertices.Count - 1;
                        objContent.AppendLine($"v {transformedVertex.X:F8} {transformedVertex.Y:F8} {transformedVertex.Z:F8}");
                    }
                }

                // Processar triângulos com normais usando mapeamento de vértices únicos
                for (int i = 0; i < mesh.NumTriangles; i++)
                {
                    MeshTriangle triangle = mesh.get_Triangle(i);

                    int originalIdx1 = (int)triangle.get_Index(0);
                    int originalIdx2 = (int)triangle.get_Index(1);
                    int originalIdx3 = (int)triangle.get_Index(2);

                    // Obter vértices originais e transformados
                    XYZ originalV1 = mesh.Vertices[originalIdx1];
                    XYZ originalV2 = mesh.Vertices[originalIdx2];
                    XYZ originalV3 = mesh.Vertices[originalIdx3];

                    XYZ v1 = transform.OfPoint(originalV1);
                    XYZ v2 = transform.OfPoint(originalV2);
                    XYZ v3 = transform.OfPoint(originalV3);

                    // Encontrar índices dos vértices únicos
                    string key1 = $"{v1.X:F8}_{v1.Y:F8}_{v1.Z:F8}";
                    string key2 = $"{v2.X:F8}_{v2.Y:F8}_{v2.Z:F8}";
                    string key3 = $"{v3.X:F8}_{v3.Y:F8}_{v3.Z:F8}";

                    int uniqueIdx1 = vertexMap[key1];
                    int uniqueIdx2 = vertexMap[key2];
                    int uniqueIdx3 = vertexMap[key3];

                    // Calcular normal com maior precisão
                    XYZ edge1 = v2 - v1;
                    XYZ edge2 = v3 - v1;
                    XYZ normal = edge1.CrossProduct(edge2);

                    // Normalizar apenas se o vetor não for zero
                    if (normal.GetLength() > 1e-10)
                    {
                        normal = normal.Normalize();
                    }
                    else
                    {
                        normal = new XYZ(0, 0, 1); // Normal padrão para triângulos degenerados
                    }

                    objContent.AppendLine($"vn {normal.X:F8} {normal.Y:F8} {normal.Z:F8}");

                    // Criar face usando índices únicos
                    int faceV1 = vertexCount + uniqueIdx1;
                    int faceV2 = vertexCount + uniqueIdx2;
                    int faceV3 = vertexCount + uniqueIdx3;

                    objContent.AppendLine($"f {faceV1}//{normalCount} {faceV2}//{normalCount} {faceV3}//{normalCount}");
                    normalCount++;
                }

                vertexCount += uniqueVertices.Count;
                return (vertexCount, normalCount);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao processar mesh: {ex.Message}");
                return (vertexCount, normalCount);
            }
        }

        private int ProcessCurveMaxQuality(Curve curve, System.Text.StringBuilder objContent, int vertexCount, Transform transform)
        {
            try
            {
                // Tessellate curve with ULTRA-HIGH quality - múltiplas tentativas
                IList<XYZ> points = null;

                // Tentativa 1: Tessellação com parâmetros customizados para máxima qualidade
                try
                {
                    // Para curvas paramétricas, usar subdivisão muito fina
                    double curveLength = curve.Length;
                    int numPoints = Math.Max(50, (int)(curveLength * 100)); // Mínimo 50 pontos, ou 100 pontos por unidade

                    points = new List<XYZ>();
                    for (int i = 0; i <= numPoints; i++)
                    {
                        double parameter = curve.GetEndParameter(0) +
                                         (curve.GetEndParameter(1) - curve.GetEndParameter(0)) * i / numPoints;
                        XYZ point = curve.Evaluate(parameter, false);
                        points.Add(point);
                    }
                    objContent.AppendLine($"# Curve tessellated with {points.Count} custom high-quality points");
                }
                catch
                {
                    // Fallback: tessellação padrão
                    points = curve.Tessellate();
                    objContent.AppendLine($"# Curve tessellated with {points.Count} standard points");
                }

                foreach (XYZ point in points)
                {
                    XYZ transformedPoint = transform.OfPoint(point);
                    objContent.AppendLine($"v {transformedPoint.X:F8} {transformedPoint.Y:F8} {transformedPoint.Z:F8}");
                }

                // Create line segments for wireframe representation
                for (int i = 0; i < points.Count - 1; i++)
                {
                    objContent.AppendLine($"l {vertexCount + i} {vertexCount + i + 1}");
                }

                return vertexCount + points.Count;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao processar curva: {ex.Message}");
                return vertexCount;
            }
        }

        private int ProcessSolid(Solid solid, System.Text.StringBuilder objContent, int vertexCount)
        {
            try
            {
                foreach (Face face in solid.Faces)
                {
                    Mesh mesh = face.Triangulate();
                    if (mesh != null)
                    {
                        // Adicionar vértices
                        for (int i = 0; i < mesh.NumTriangles; i++)
                        {
                            MeshTriangle triangle = mesh.get_Triangle(i);

                            XYZ v1 = mesh.Vertices[(int)triangle.get_Index(0)];
                            XYZ v2 = mesh.Vertices[(int)triangle.get_Index(1)];
                            XYZ v3 = mesh.Vertices[(int)triangle.get_Index(2)];

                            objContent.AppendLine($"v {v1.X:F6} {v1.Y:F6} {v1.Z:F6}");
                            objContent.AppendLine($"v {v2.X:F6} {v2.Y:F6} {v2.Z:F6}");
                            objContent.AppendLine($"v {v3.X:F6} {v3.Y:F6} {v3.Z:F6}");

                            objContent.AppendLine($"f {vertexCount} {vertexCount + 1} {vertexCount + 2}");
                            vertexCount += 3;
                        }
                    }
                }
                return vertexCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao processar sólido: {ex.Message}");
                return vertexCount;
            }
        }

        private int CreateBoundingBoxOBJ(BoundingBoxXYZ bbox, System.Text.StringBuilder objContent, int vertexCount)
        {
            XYZ min = bbox.Min;
            XYZ max = bbox.Max;

            // Criar vértices do bounding box
            objContent.AppendLine($"v {min.X:F6} {min.Y:F6} {min.Z:F6}");
            objContent.AppendLine($"v {max.X:F6} {min.Y:F6} {min.Z:F6}");
            objContent.AppendLine($"v {max.X:F6} {max.Y:F6} {min.Z:F6}");
            objContent.AppendLine($"v {min.X:F6} {max.Y:F6} {min.Z:F6}");
            objContent.AppendLine($"v {min.X:F6} {min.Y:F6} {max.Z:F6}");
            objContent.AppendLine($"v {max.X:F6} {min.Y:F6} {max.Z:F6}");
            objContent.AppendLine($"v {max.X:F6} {max.Y:F6} {max.Z:F6}");
            objContent.AppendLine($"v {min.X:F6} {max.Y:F6} {max.Z:F6}");

            // Criar faces do cubo
            objContent.AppendLine($"f {vertexCount} {vertexCount + 1} {vertexCount + 2} {vertexCount + 3}");
            objContent.AppendLine($"f {vertexCount + 4} {vertexCount + 7} {vertexCount + 6} {vertexCount + 5}");
            objContent.AppendLine($"f {vertexCount} {vertexCount + 4} {vertexCount + 5} {vertexCount + 1}");
            objContent.AppendLine($"f {vertexCount + 1} {vertexCount + 5} {vertexCount + 6} {vertexCount + 2}");
            objContent.AppendLine($"f {vertexCount + 2} {vertexCount + 6} {vertexCount + 7} {vertexCount + 3}");
            objContent.AppendLine($"f {vertexCount + 4} {vertexCount} {vertexCount + 3} {vertexCount + 7}");

            return vertexCount + 8;
        }

        private int CreateHighQualityBoundingBoxOBJ(BoundingBoxXYZ bbox, System.Text.StringBuilder objContent, int vertexCount)
        {
            XYZ min = bbox.Min;
            XYZ max = bbox.Max;

            objContent.AppendLine("# High quality bounding box with detailed subdivision");

            // Criar subdivisões para maior qualidade
            int subdivisions = 4; // 4x4x4 = 64 cubos pequenos para maior detalhe
            double stepX = (max.X - min.X) / subdivisions;
            double stepY = (max.Y - min.Y) / subdivisions;
            double stepZ = (max.Z - min.Z) / subdivisions;

            int startVertex = vertexCount;

            // Criar grid de vértices
            for (int i = 0; i <= subdivisions; i++)
            {
                for (int j = 0; j <= subdivisions; j++)
                {
                    for (int k = 0; k <= subdivisions; k++)
                    {
                        double x = min.X + i * stepX;
                        double y = min.Y + j * stepY;
                        double z = min.Z + k * stepZ;
                        objContent.AppendLine($"v {x:F8} {y:F8} {z:F8}");
                    }
                }
            }

            // Criar faces com alta qualidade
            int gridSize = subdivisions + 1;
            for (int i = 0; i < subdivisions; i++)
            {
                for (int j = 0; j < subdivisions; j++)
                {
                    for (int k = 0; k < subdivisions; k++)
                    {
                        // Calcular índices dos vértices do cubo
                        int v000 = startVertex + i * gridSize * gridSize + j * gridSize + k;
                        int v001 = v000 + 1;
                        int v010 = v000 + gridSize;
                        int v011 = v010 + 1;
                        int v100 = v000 + gridSize * gridSize;
                        int v101 = v100 + 1;
                        int v110 = v100 + gridSize;
                        int v111 = v110 + 1;

                        // Criar 12 triângulos para cada cubo (6 faces x 2 triângulos)
                        // Face frontal
                        objContent.AppendLine($"f {v000} {v010} {v011}");
                        objContent.AppendLine($"f {v000} {v011} {v001}");
                        // Face traseira
                        objContent.AppendLine($"f {v100} {v111} {v110}");
                        objContent.AppendLine($"f {v100} {v101} {v111}");
                        // Face esquerda
                        objContent.AppendLine($"f {v000} {v100} {v110}");
                        objContent.AppendLine($"f {v000} {v110} {v010}");
                        // Face direita
                        objContent.AppendLine($"f {v001} {v011} {v111}");
                        objContent.AppendLine($"f {v001} {v111} {v101}");
                        // Face inferior
                        objContent.AppendLine($"f {v000} {v001} {v101}");
                        objContent.AppendLine($"f {v000} {v101} {v100}");
                        // Face superior
                        objContent.AppendLine($"f {v010} {v110} {v111}");
                        objContent.AppendLine($"f {v010} {v111} {v011}");
                    }
                }
            }

            return startVertex + gridSize * gridSize * gridSize;
        }

        private void CreateHighQualityCube(System.Text.StringBuilder objContent)
        {
            objContent.AppendLine("# High quality reference cube with smooth surfaces");

            // Criar cubo subdividido para maior qualidade
            int subdivisions = 8; // 8x8x8 subdivisões
            double size = 2.0; // Cubo de 2x2x2 metros
            double step = size / subdivisions;
            double halfSize = size / 2.0;

            int vertexCount = 1;

            // Criar grid de vértices
            for (int i = 0; i <= subdivisions; i++)
            {
                for (int j = 0; j <= subdivisions; j++)
                {
                    for (int k = 0; k <= subdivisions; k++)
                    {
                        double x = -halfSize + i * step;
                        double y = -halfSize + j * step;
                        double z = -halfSize + k * step;
                        objContent.AppendLine($"v {x:F8} {y:F8} {z:F8}");
                    }
                }
            }

            // Criar faces do cubo subdividido
            int gridSize = subdivisions + 1;
            for (int i = 0; i < subdivisions; i++)
            {
                for (int j = 0; j < subdivisions; j++)
                {
                    for (int k = 0; k < subdivisions; k++)
                    {
                        // Apenas criar faces nas superfícies externas
                        bool isExternalFace = (i == 0 || i == subdivisions - 1 ||
                                             j == 0 || j == subdivisions - 1 ||
                                             k == 0 || k == subdivisions - 1);

                        if (isExternalFace)
                        {
                            int v000 = vertexCount + i * gridSize * gridSize + j * gridSize + k;
                            int v001 = v000 + 1;
                            int v010 = v000 + gridSize;
                            int v011 = v010 + 1;
                            int v100 = v000 + gridSize * gridSize;
                            int v101 = v100 + 1;
                            int v110 = v100 + gridSize;
                            int v111 = v110 + 1;

                            // Criar faces apenas nas superfícies externas
                            if (i == 0) // Face frontal
                            {
                                objContent.AppendLine($"f {v000} {v010} {v011}");
                                objContent.AppendLine($"f {v000} {v011} {v001}");
                            }
                            if (i == subdivisions - 1) // Face traseira
                            {
                                objContent.AppendLine($"f {v100} {v111} {v110}");
                                objContent.AppendLine($"f {v100} {v101} {v111}");
                            }
                            if (j == 0) // Face esquerda
                            {
                                objContent.AppendLine($"f {v000} {v100} {v110}");
                                objContent.AppendLine($"f {v000} {v110} {v010}");
                            }
                            if (j == subdivisions - 1) // Face direita
                            {
                                objContent.AppendLine($"f {v001} {v011} {v111}");
                                objContent.AppendLine($"f {v001} {v111} {v101}");
                            }
                            if (k == 0) // Face inferior
                            {
                                objContent.AppendLine($"f {v000} {v001} {v101}");
                                objContent.AppendLine($"f {v000} {v101} {v100}");
                            }
                            if (k == subdivisions - 1) // Face superior
                            {
                                objContent.AppendLine($"f {v010} {v110} {v111}");
                                objContent.AppendLine($"f {v010} {v111} {v011}");
                            }
                        }
                    }
                }
            }
        }

        private void CreateBasicCube(System.Text.StringBuilder objContent)
        {
            objContent.AppendLine("v -1.0 -1.0 -1.0");
            objContent.AppendLine("v 1.0 -1.0 -1.0");
            objContent.AppendLine("v 1.0 1.0 -1.0");
            objContent.AppendLine("v -1.0 1.0 -1.0");
            objContent.AppendLine("v -1.0 -1.0 1.0");
            objContent.AppendLine("v 1.0 -1.0 1.0");
            objContent.AppendLine("v 1.0 1.0 1.0");
            objContent.AppendLine("v -1.0 1.0 1.0");

            objContent.AppendLine("f 1 2 3 4");
            objContent.AppendLine("f 5 8 7 6");
            objContent.AppendLine("f 1 5 6 2");
            objContent.AppendLine("f 2 6 7 3");
            objContent.AppendLine("f 3 7 8 4");
            objContent.AppendLine("f 5 1 4 8");
        }

        private string CreateSimpleOBJ(Document doc)
        {
            // Criar um arquivo OBJ básico como fallback
            var objContent = new System.Text.StringBuilder();
            objContent.AppendLine("# BIMEX Family Export - Simple OBJ");
            objContent.AppendLine("# Generated by BIMEX Developer Plugin");
            objContent.AppendLine();

            CreateBasicCube(objContent);

            return objContent.ToString();
        }

        private (int, int) ExtractFamilyDocumentGeometry(Document familyDoc, System.Text.StringBuilder objContent, int vertexCount, int normalCount)
        {
            try
            {
                objContent.AppendLine("# Extracting geometry from family document");

                // Configurar opções de geometria para máxima qualidade
                Options geomOptions = new Options();
                geomOptions.DetailLevel = ViewDetailLevel.Fine;
                geomOptions.IncludeNonVisibleObjects = true;
                geomOptions.ComputeReferences = true;

                // Coletar toda a geometria do documento da família
                var collector = new FilteredElementCollector(familyDoc)
                    .WhereElementIsNotElementType()
                    .Where(e => e.Category != null);

                foreach (Element element in collector)
                {
                    GeometryElement geomElement = element.get_Geometry(geomOptions);
                    if (geomElement != null)
                    {
                        objContent.AppendLine($"# Processing family element: {element.Category.Name}");
                        var result = ProcessGeometryMaxQuality(geomElement, objContent, vertexCount, normalCount, Transform.Identity);
                        vertexCount = result.Item1;
                        normalCount = result.Item2;
                    }
                }

                return (vertexCount, normalCount);
            }
            catch (Exception ex)
            {
                objContent.AppendLine($"# Error extracting family document geometry: {ex.Message}");
                return (vertexCount, normalCount);
            }
        }

        private View3D EnsureProper3DView(Document doc)
        {
            try
            {
                View3D view3D = null;

                using (Transaction trans = new Transaction(doc, "Ensure Proper 3D View for Export"))
                {
                    trans.Start();

                    // Primeiro, tentar encontrar uma vista 3D existente
                    var view3DCollector = new FilteredElementCollector(doc)
                        .OfClass(typeof(View3D))
                        .Cast<View3D>()
                        .Where(v => !v.IsTemplate);

                    view3D = view3DCollector.FirstOrDefault();

                    if (view3D == null)
                    {
                        // Criar uma nova vista 3D se não existir
                        ViewFamilyType viewFamilyType = new FilteredElementCollector(doc)
                            .OfClass(typeof(ViewFamilyType))
                            .Cast<ViewFamilyType>()
                            .Where(vft => vft.ViewFamily == ViewFamily.ThreeDimensional)
                            .FirstOrDefault();

                        if (viewFamilyType != null)
                        {
                            view3D = View3D.CreateIsometric(doc, viewFamilyType.Id);
                            view3D.Name = "BIMEX Export 3D View - Ultra High Quality";
                        }
                    }

                    // Configurar vista para máxima qualidade
                    if (view3D != null)
                    {
                        view3D.DetailLevel = ViewDetailLevel.Fine;
                        view3D.DisplayStyle = DisplayStyle.Shading;

                        // Configurações específicas para exportação
                        try
                        {
                            view3D.CropBoxActive = false;
                            view3D.CropBoxVisible = false;
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"Configuração de crop box falhou: {ex.Message}");
                        }
                    }

                    trans.Commit();
                }

                // Forçar regeneração
                if (view3D != null)
                {
                    doc.Regenerate();
                }

                return view3D;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao garantir vista 3D: {ex.Message}");
                return null;
            }
        }

        private View3D GetOrCreate3DView(Document doc)
        {
            return EnsureProper3DView(doc);
        }

        private string CreateEmergencyOBJ(Document doc)
        {
            try
            {
                var objContent = new System.Text.StringBuilder();
                objContent.AppendLine("# BIMEX Family Export - EMERGENCY OBJ");
                objContent.AppendLine("# Generated by BIMEX Developer Plugin");
                objContent.AppendLine($"# Export Date: {DateTime.Now}");
                objContent.AppendLine("# Note: Emergency fallback - basic geometry");
                objContent.AppendLine();

                // Tentar coletar qualquer geometria disponível
                var allElements = new FilteredElementCollector(doc)
                    .WhereElementIsNotElementType()
                    .Where(e => e.Category != null)
                    .ToList();

                if (allElements.Count > 0)
                {
                    objContent.AppendLine($"# Found {allElements.Count} elements in document");

                    int vertexCount = 1;
                    foreach (Element element in allElements.Take(10)) // Limitar a 10 elementos
                    {
                        try
                        {
                            BoundingBoxXYZ bbox = element.get_BoundingBox(null);
                            if (bbox != null)
                            {
                                objContent.AppendLine($"# Element: {element.Category.Name} (ID: {element.Id})");
                                vertexCount = CreateBoundingBoxOBJ(bbox, objContent, vertexCount);
                            }
                        }
                        catch (Exception ex)
                        {
                            objContent.AppendLine($"# Error processing element {element.Id}: {ex.Message}");
                        }
                    }
                }
                else
                {
                    objContent.AppendLine("# No elements found - creating reference cube");
                    CreateBasicCube(objContent);
                }

                return objContent.ToString();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao criar OBJ de emergência: {ex.Message}");
                return CreateSimpleOBJ(doc);
            }
        }

        private void EnsureActiveView3D(Document doc)
        {
            try
            {
                // Procurar vista 3D BIMEX criada anteriormente
                var view3DCollector = new FilteredElementCollector(doc)
                    .OfClass(typeof(View3D))
                    .Cast<View3D>()
                    .Where(v => !v.IsTemplate && v.Name.Contains("BIMEX"));

                View3D bimexView3D = view3DCollector.FirstOrDefault();

                if (bimexView3D == null)
                {
                    // Se não encontrar vista BIMEX, procurar qualquer vista 3D
                    var allView3DCollector = new FilteredElementCollector(doc)
                        .OfClass(typeof(View3D))
                        .Cast<View3D>()
                        .Where(v => !v.IsTemplate);

                    bimexView3D = allView3DCollector.FirstOrDefault();
                }

                if (bimexView3D != null)
                {
                    System.Diagnostics.Debug.WriteLine($"Vista 3D encontrada para salvamento: {bimexView3D.Name}");

                    // Forçar regeneração na vista 3D
                    doc.Regenerate();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("Nenhuma vista 3D encontrada para salvamento");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao garantir vista 3D ativa: {ex.Message}");
            }
        }

        private string SaveRevitModel(Document doc)
        {
            try
            {
                // Garantir que a vista 3D está ativa antes de salvar
                EnsureActiveView3D(doc);

                // Criar pasta BIMEX na área de trabalho para modelos Revit
                string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                string bimexFolder = System.IO.Path.Combine(desktopPath, "BIMEX_Revit_Models");
                System.IO.Directory.CreateDirectory(bimexFolder);

                // Gerar nome do arquivo com timestamp
                string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                string fileName = $"BIMEX_FamilyModel_{timestamp}.rvt";
                string fullPath = System.IO.Path.Combine(bimexFolder, fileName);

                // Obter informações da família para o nome do arquivo
                var familyInstances = new FilteredElementCollector(doc)
                    .OfClass(typeof(FamilyInstance))
                    .Cast<FamilyInstance>()
                    .ToList();

                if (familyInstances.Count > 0)
                {
                    var firstFamily = familyInstances.First();
                    string familyName = firstFamily.Symbol.FamilyName;
                    // Limpar caracteres inválidos do nome da família
                    familyName = System.IO.Path.GetInvalidFileNameChars()
                        .Aggregate(familyName, (current, c) => current.Replace(c, '_'));

                    fileName = $"BIMEX_{familyName}_{timestamp}.rvt";
                    fullPath = System.IO.Path.Combine(bimexFolder, fileName);
                }

                // CORREÇÃO: Salvar fora de transação para evitar conflitos
                try
                {
                    // Configurar opções de salvamento
                    SaveAsOptions saveOptions = new SaveAsOptions();
                    saveOptions.OverwriteExistingFile = true;
                    saveOptions.Compact = true; // Compactar arquivo para menor tamanho

                    // Salvar o documento (fora de transação)
                    doc.SaveAs(fullPath, saveOptions);

                    System.Diagnostics.Debug.WriteLine($"Modelo Revit salvo com sucesso: {fullPath}");
                    return fullPath;
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Erro ao salvar modelo Revit com opções: {ex.Message}");

                    // Tentar salvar sem opções especiais
                    try
                    {
                        doc.SaveAs(fullPath);
                        System.Diagnostics.Debug.WriteLine($"Modelo Revit salvo (método alternativo): {fullPath}");
                        return fullPath;
                    }
                    catch (Exception ex2)
                    {
                        System.Diagnostics.Debug.WriteLine($"Erro no método alternativo: {ex2.Message}");

                        // Último recurso: tentar salvar com nome mais simples
                        try
                        {
                            string simplePath = System.IO.Path.Combine(bimexFolder, $"BIMEX_Model_{timestamp}.rvt");
                            doc.SaveAs(simplePath);
                            System.Diagnostics.Debug.WriteLine($"Modelo Revit salvo (nome simplificado): {simplePath}");
                            return simplePath;
                        }
                        catch (Exception ex3)
                        {
                            System.Diagnostics.Debug.WriteLine($"Erro no último recurso: {ex3.Message}");
                            return null;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro geral ao salvar modelo Revit: {ex.Message}");
                return null;
            }
        }
    }

    // Classe da Aplicação para criar a aba e botão no Revit
    public class App : IExternalApplication
    {
        public Result OnStartup(UIControlledApplication application)
        {
            try
            {
                // Criar uma nova aba "BIMEX Developer"
                string tabName = "BIMEX Developer";
                application.CreateRibbonTab(tabName);

                // Criar painel dentro da aba
                string panelName = "Family Tools";
                RibbonPanel ribbonPanel = application.CreateRibbonPanel(tabName, panelName);

                // Informações do assembly
                string assemblyPath = System.Reflection.Assembly.GetExecutingAssembly().Location;

                // Criar botão Family Export
                PushButtonData buttonData = new PushButtonData(
                    "FamilyExportButton",
                    "Family Export",
                    assemblyPath,
                    "BimexDeveloperPlugin.FamilyExportCommand");

                buttonData.ToolTip = "Cria um novo modelo, adiciona piso 5x5, carrega família na origem, remove o piso e salva OBJ + Revit";
                buttonData.LongDescription = "Esta ferramenta cria um novo modelo do zero, adiciona um piso 5x5 centralizado na origem, permite selecionar uma família (.rfa), carrega a família na origem, remove o piso temporário, exporta para formato OBJ com máxima qualidade e salva o modelo Revit resultante.";

                // Adicionar o botão ao painel
                PushButton pushButton = ribbonPanel.AddItem(buttonData) as PushButton;

                return Result.Succeeded;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Erro ao inicializar plugin: {ex.Message}");
                return Result.Failed;
            }
        }

        public Result OnShutdown(UIControlledApplication application)
        {
            return Result.Succeeded;
        }
    }
}
